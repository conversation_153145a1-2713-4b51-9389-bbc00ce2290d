{"version": 3, "file": "message.service.js", "sourceRoot": "", "sources": ["../../src/chat/message.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,qDAA2C;AAGpC,IAAM,cAAc,GAApB,MAAM,cAAc;IAGf;IAFV,YAEU,WAAgC;QAAhC,gBAAW,GAAX,WAAW,CAAqB;IACvC,CAAC;IAEJ,KAAK,CAAC,kBAAkB,CAAC,IAAsB;QAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAU;QAC9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,EAAE;gBAC/C,MAAM,EAAE,WAAW;gBACnB,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,iCAAiC,CAAC,CAAC;YAC1E,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IAGD,KAAK,CAAC,yBAAyB,CAAC,MAAc;QAC5C,OAAO,CAAC,IAAI,CAAC,qFAAqF,CAAC,CAAC;QACpG,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,6BAA6B,CAAC,QAAgB;QAClD,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,2CAA2C,QAAQ,EAAE,CAAC,CAAC;YACnE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBAC3C,KAAK,EAAE,EAAE,iBAAiB,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE;aAC1D,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ,CAAC,MAAM,yBAAyB,QAAQ,EAAE,CAAC,CAAC;YACzE,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;oBAC9B,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,GAAG,CAAC,IAAI,QAAQ,CAAC,MAAM,QAAQ,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,eAAe,UAAU,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC9H,CAAC,CAAC,CAAC;YACL,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,QAAgB;QAC9C,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,uCAAuC,QAAQ,EAAE,CAAC,CAAC;YAC/D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBAC3C,KAAK,EAAE;oBACL,EAAE,eAAe,EAAE,QAAQ,EAAE;oBAC7B,EAAE,iBAAiB,EAAE,QAAQ,EAAE;iBAChC;gBACD,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;aAC5B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ,CAAC,MAAM,uBAAuB,QAAQ,EAAE,CAAC,CAAC;YACvE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,SAAiB,EAAE,SAAiB;QACnE,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,8CAA8C,SAAS,QAAQ,SAAS,EAAE,CAAC,CAAC;YACxF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBAC3C,KAAK,EAAE;oBACL,EAAE,eAAe,EAAE,SAAS,EAAE,iBAAiB,EAAE,SAAS,EAAE;oBAC5D,EAAE,eAAe,EAAE,SAAS,EAAE,iBAAiB,EAAE,SAAS,EAAE;iBAC7D;gBACD,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;aAC5B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ,CAAC,MAAM,4BAA4B,CAAC,CAAC;YAClE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,QAAQ,EAAE,CAAC,CAAC;YACjE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC3C,iBAAiB,EAAE,QAAQ;gBAC3B,MAAM,EAAE,WAAW;aACpB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,WAAW,MAAM,CAAC,QAAQ,IAAI,CAAC,2BAA2B,QAAQ,EAAE,CAAC,CAAC;YAClF,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,6BAA6B,CAAC,cAAsB;QACxD,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wCAAwC,cAAc,EAAE,CAAC,CAAC;YACtE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBAC3C,KAAK,EAAE;oBACL,eAAe,EAAE,cAAc;oBAC/B,MAAM,EAAE,WAAW;iBACpB;gBACD,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;aAC5B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ,CAAC,MAAM,+BAA+B,cAAc,EAAE,CAAC,CAAC;YACrF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,cAAc,GAAG,EAAE,KAAK,CAAC,CAAC;YACxF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AApIY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,wBAAO,CAAC,CAAA;qCACL,oBAAU;GAHtB,cAAc,CAoI1B"}