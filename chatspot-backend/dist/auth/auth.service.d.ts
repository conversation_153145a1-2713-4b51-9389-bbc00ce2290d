import { Repository } from 'typeorm';
import { User } from './user.entity';
import { JwtService } from '@nestjs/jwt';
export declare class AuthService {
    private userRepo;
    private jwtService;
    constructor(userRepo: Repository<User>, jwtService: JwtService);
    register(username: string, password: string): Promise<User>;
    login(username: string, password: string): Promise<{
        access_token: string;
    }>;
}
