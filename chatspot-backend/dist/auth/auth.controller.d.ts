import { AuthService } from './auth.service';
import { AuthCredentialsDto, AuthResponseDto, UserDto } from './dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(authCredentialsDto: AuthCredentialsDto): Promise<UserDto>;
    login(authCredentialsDto: AuthCredentialsDto): Promise<AuthResponseDto>;
    getCurrentUser(req: any): Promise<UserDto>;
}
