import { Repository } from 'typeorm';
import { FcmToken } from './entities/fcm-token.entity';
import { ConfigService } from '@nestjs/config';
export declare class NotificationsService {
    private fcmTokenRepo;
    private configService;
    private readonly logger;
    private firebaseInitialized;
    constructor(fcmTokenRepo: Repository<FcmToken>, configService: ConfigService);
    private initializeFirebase;
    registerToken(username: string, token: string, deviceInfo?: string): Promise<FcmToken>;
    getActiveTokensForUser(username: string): Promise<FcmToken[]>;
    deactivateToken(token: string): Promise<void>;
    sendNotification(username: string, title: string, body: string, data?: Record<string, string>): Promise<boolean>;
}
