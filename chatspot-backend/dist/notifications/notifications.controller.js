"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var NotificationsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsController = void 0;
const common_1 = require("@nestjs/common");
const notifications_service_1 = require("./notifications.service");
const register_token_dto_1 = require("./dto/register-token.dto");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const swagger_1 = require("@nestjs/swagger");
let NotificationsController = NotificationsController_1 = class NotificationsController {
    notificationsService;
    logger = new common_1.Logger(NotificationsController_1.name);
    constructor(notificationsService) {
        this.notificationsService = notificationsService;
    }
    async registerToken(registerTokenDto) {
        this.logger.log(`Registering FCM token for user ${registerTokenDto.userId}`);
        const result = await this.notificationsService.registerToken(registerTokenDto.userId, registerTokenDto.token, registerTokenDto.deviceInfo);
        return {
            success: true,
            message: 'FCM token registered successfully',
            data: {
                id: result.id,
                username: result.username,
                created_at: result.created_at,
            },
        };
    }
    async sendTestNotification(username) {
        this.logger.log(`Sending test notification to user ${username}`);
        const result = await this.notificationsService.sendNotification(username, 'Test Notification', 'This is a test notification from Chatspot', {
            type: 'test',
            timestamp: Date.now().toString(),
        });
        return {
            success: result,
            message: result ? 'Test notification sent successfully' : 'Failed to send test notification',
        };
    }
};
exports.NotificationsController = NotificationsController;
__decorate([
    (0, common_1.Post)('register-token'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Register FCM token' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Token registered successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [register_token_dto_1.RegisterTokenDto]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "registerToken", null);
__decorate([
    (0, common_1.Get)('test/:username'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Send test notification' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Test notification sent' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('username')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "sendTestNotification", null);
exports.NotificationsController = NotificationsController = NotificationsController_1 = __decorate([
    (0, swagger_1.ApiTags)('notifications'),
    (0, common_1.Controller)('api/notifications'),
    __metadata("design:paramtypes", [notifications_service_1.NotificationsService])
], NotificationsController);
//# sourceMappingURL=notifications.controller.js.map