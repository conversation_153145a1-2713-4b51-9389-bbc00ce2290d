import { AdminService } from './admin.service';
import { UserDto } from '../auth/dto';
declare class CreateUserDto {
    username: string;
    password: string;
    isAdmin?: boolean;
}
declare class UpdateUserDto {
    username?: string;
    password?: string;
    isAdmin?: boolean;
}
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    getAllUsers(): Promise<UserDto[]>;
    getUserById(userId: string): Promise<UserDto>;
    createUser(createUserDto: CreateUserDto): Promise<UserDto>;
    updateUser(userId: string, updateUserDto: UpdateUserDto): Promise<UserDto>;
    deleteUser(userId: string): Promise<{
        message: string;
    }>;
}
export {};
