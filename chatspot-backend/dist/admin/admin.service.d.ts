import { Repository } from 'typeorm';
import { User } from '../auth/user.entity';
import { UserDto } from '../auth/dto';
export declare class AdminService {
    private userRepo;
    constructor(userRepo: Repository<User>);
    getAllUsers(): Promise<UserDto[]>;
    getUserById(userId: string): Promise<UserDto>;
    createUser(username: string, password: string, isAdmin?: boolean): Promise<UserDto>;
    updateUser(userId: string, updates: {
        username?: string;
        password?: string;
        isAdmin?: boolean;
    }): Promise<UserDto>;
    deleteUser(userId: string): Promise<{
        message: string;
    }>;
}
