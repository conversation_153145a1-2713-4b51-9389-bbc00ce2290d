"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../auth/user.entity");
const bcrypt = require("bcrypt");
let AdminService = class AdminService {
    userRepo;
    constructor(userRepo) {
        this.userRepo = userRepo;
    }
    async getAllUsers() {
        const users = await this.userRepo.find();
        return users.map(user => ({
            id: user.id,
            username: user.username,
            isAdmin: user.isAdmin,
        }));
    }
    async getUserById(userId) {
        const user = await this.userRepo.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID "${userId}" not found`);
        }
        return {
            id: user.id,
            username: user.username,
            isAdmin: user.isAdmin,
        };
    }
    async createUser(username, password, isAdmin = false) {
        if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
            throw new common_1.BadRequestException('Invalid username format');
        }
        if (!/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(password)) {
            throw new common_1.BadRequestException('Password too weak');
        }
        const existingUser = await this.userRepo.findOne({ where: { username } });
        if (existingUser) {
            throw new common_1.ConflictException('Username already exists');
        }
        const hashedPassword = await bcrypt.hash(password, 10);
        const newUser = this.userRepo.create({
            username,
            password: hashedPassword,
            isAdmin,
        });
        const savedUser = await this.userRepo.save(newUser);
        return {
            id: savedUser.id,
            username: savedUser.username,
            isAdmin: savedUser.isAdmin,
        };
    }
    async updateUser(userId, updates) {
        const user = await this.userRepo.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID "${userId}" not found`);
        }
        if (updates.username) {
            if (!/^[a-zA-Z0-9_]{3,20}$/.test(updates.username)) {
                throw new common_1.BadRequestException('Invalid username format');
            }
            if (updates.username !== user.username) {
                const existingUser = await this.userRepo.findOne({ where: { username: updates.username } });
                if (existingUser) {
                    throw new common_1.ConflictException('Username already exists');
                }
                user.username = updates.username;
            }
        }
        if (updates.password) {
            if (!/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(updates.password)) {
                throw new common_1.BadRequestException('Password too weak');
            }
            user.password = await bcrypt.hash(updates.password, 10);
        }
        if (updates.isAdmin !== undefined) {
            user.isAdmin = updates.isAdmin;
        }
        const updatedUser = await this.userRepo.save(user);
        return {
            id: updatedUser.id,
            username: updatedUser.username,
            isAdmin: updatedUser.isAdmin,
        };
    }
    async deleteUser(userId) {
        const user = await this.userRepo.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID "${userId}" not found`);
        }
        await this.userRepo.remove(user);
        return { message: `User ${user.username} successfully deleted` };
    }
};
exports.AdminService = AdminService;
exports.AdminService = AdminService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AdminService);
//# sourceMappingURL=admin.service.js.map