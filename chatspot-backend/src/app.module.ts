import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthModule } from './auth/auth.module';
import { ChatModule } from './chat/chat.module';
import { AdminModule } from './admin/admin.module';
import { NotificationsModule } from './notifications/notifications.module';
import { User } from './auth/user.entity';
import { Message } from './chat/message.entity';
import { FcmToken } from './notifications/entities/fcm-token.entity';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        // Check if DATABASE_URL is provided (for Render deployment)
        const databaseUrl = configService.get('DATABASE_URL');

        const useInMemory = configService.get('USE_IN_MEMORY_DB') === 'true';

        if (useInMemory) {
          console.log('Using SQLite in-memory database for development');
          return {
            type: 'sqlite',
            database: ':memory:',
            entities: [User, Message, FcmToken],
            synchronize: true,
            logging: true,
          };
        } else if (databaseUrl) {
          console.log('Using PostgreSQL database with connection URL', databaseUrl);
          return {
            type: 'postgres',
            url: databaseUrl,
            entities: [User, Message, FcmToken],
            synchronize: configService.get('NODE_ENV') !== 'production',
            ssl: { rejectUnauthorized: false },
          };
        }

        // Use individual connection parameters if DATABASE_URL is not provided
        return {
          type: 'postgres',
          host: configService.get('DB_HOST', 'localhost'),
          port: configService.get('DB_PORT', 5432),
          username: configService.get('DB_USERNAME', 'chatuser'),
          password: configService.get('DB_PASSWORD', 'chatpassword'),
          database: configService.get('DB_DATABASE', 'chatdb'),
          entities: [User, Message, FcmToken],
          synchronize: configService.get('NODE_ENV') !== 'production',
        };
      },
    }),
    AuthModule,
    ChatModule,
    AdminModule,
    NotificationsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
