import { <PERSON>ti<PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('fcm_tokens')
export class FcmToken {
  @ApiProperty({
    description: 'Unique identifier for the FCM token record',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Username of the user',
    example: 'johndoe',
  })
  @Column()
  username: string;

  @ApiProperty({
    description: 'Firebase Cloud Messaging token',
    example: 'fMqXXEFYQ-GNRaq7tUHXm6:APA91bHqH...',
  })
  @Column('text')
  token: string;

  @ApiProperty({
    description: 'Device information',
    example: 'Chrome 98.0.4758.102 on Windows 10',
    nullable: true,
  })
  @Column({ nullable: true })
  device_info: string;

  @ApiProperty({
    description: 'Whether the token is active',
    example: true,
  })
  @Column({ default: true })
  is_active: boolean;

  @ApiProperty({
    description: 'Timestamp when the token was created',
    example: '2023-10-15T14:30:00Z',
  })
  @CreateDateColumn()
  created_at: Date;

  @ApiProperty({
    description: 'Timestamp when the token was last updated',
    example: '2023-10-15T14:30:00Z',
  })
  @UpdateDateColumn()
  updated_at: Date;

  @ApiProperty({
    description: 'Timestamp when the token was last used',
    example: '2023-10-15T14:30:00Z',
    nullable: true,
  })
  @Column({ type: 'timestamp', nullable: true })
  last_used_at: Date | null;
}
