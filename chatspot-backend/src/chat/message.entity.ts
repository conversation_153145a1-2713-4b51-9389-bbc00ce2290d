import { Entity, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('messages')
export class Message {
  @ApiProperty({
    description: 'Unique identifier for the message',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Username of the sender',
    example: 'johndoe',
  })
  @Column()
  sender_username: string;

  @ApiProperty({
    description: 'Username of the receiver',
    example: 'janedoe',
  })
  @Column()
  receiver_username: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how are you?',
  })
  @Column('text')
  message: string;

  @ApiProperty({
    description: 'Timestamp when the message was sent',
    example: '2023-10-15T14:30:00Z',
  })
  @CreateDateColumn()
  timestamp: Date;

  @ApiProperty({
    description: 'Status of the message',
    example: 'delivered',
    enum: ['pending', 'delivered'],
  })
  @Column({ default: 'pending' })
  status: 'pending' | 'delivered';

  @ApiProperty({
    description: 'Timestamp when the message was delivered',
    example: '2023-10-15T14:30:05Z',
    nullable: true,
  })
  @Column({ type: 'timestamp', nullable: true })
  delivered_at: Date | null;

  @ApiProperty({
    description: 'Message type',
    example: 'text',
    enum: ['text', 'clear_chat', 'typing', 'delete_user', 'system'],
  })
  @Column({ default: 'text' })
  type: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'system';

  @ApiProperty({
    description: 'Client-generated message ID for tracking',
    example: 'client-msg-123',
    nullable: true,
  })
  @Column({ type: 'varchar', nullable: true })
  client_message_id: string | null;
}