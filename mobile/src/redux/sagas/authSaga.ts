import { call, put, takeLatest } from 'redux-saga/effects';
import { authService } from '../../services/api';
import {
  loginRequest,
  loginSuccess,
  loginFailure,
  registerRequest,
  registerSuccess,
  registerFailure,
  logout
} from '../slices/authSlice';
import { authStorage } from '../../utils/storage';

interface AuthResponse {
  access_token: string;
  [key: string]: any;
}

// Worker saga for login
function* loginSaga(action: any) {
  try {
    // Get username and password from action payload
    const { username, password } = action.payload;

    if (!username || !password) {
      throw new Error('Username or password not provided');
    }
console.log('username', username)
console.log('password', password)
    // Call the login API
    const response: AuthResponse = yield call(authService.login, username, password);

    console.log('response', response)

    // Store token and username in AsyncStorage for persistence
    yield call(authStorage.setToken, response.access_token);
    yield call(authStorage.setUsername, username);
    yield call(authStorage.setPreviousUsername, username);

    // Dispatch success action with the token
    yield put(loginSuccess({
      access_token: response.access_token,
      username
    }));

  } catch (error: any) {
    // Dispatch failure action with error message
    yield put(loginFailure(error.toString()));
  }
}

// Worker saga for register
function* registerSaga(action: any) {
  try {
    // Get username and password from action payload
    const { username, password } = action.payload;

    if (!username || !password) {
      throw new Error('Username or password not provided');
    }

    // Call the register API
    const response: AuthResponse = yield call(authService.register, username, password);

    // Store token and username in AsyncStorage for persistence
    yield call(authStorage.setToken, response.access_token);
    yield call(authStorage.setUsername, username);
    yield call(authStorage.setPreviousUsername, username);

    // Dispatch success action with the token
    yield put(registerSuccess({
      access_token: response.access_token,
      username
    }));

  } catch (error: any) {
    // Dispatch failure action with error message
    yield put(registerFailure(error.toString()));
  }
}

// Worker saga for logout
function* logoutSaga() {
  try {
    // Clear all auth data from AsyncStorage
    yield call(authStorage.clearAll);
  } catch (error: any) {
    console.error('Logout error:', error);
  }
}

// Watcher saga for auth actions
export function* authSaga() {
  yield takeLatest(loginRequest.type, loginSaga);
  yield takeLatest(registerRequest.type, registerSaga);
  yield takeLatest(logout.type, logoutSaga);
}
