import { configureStore } from '@reduxjs/toolkit';
const createSagaMiddleware = require('redux-saga').default
import authReducer from './slices/authSlice';
import chatDBReducer from './slices/chatDBSlice';
import socketReducer from './slices/socketSlice';
import typingReducer from './slices/typingSlice';
import emojiReactionReducer from './slices/emojiReactionSlice';
import rootSaga from './sagas/rootSaga';
import Reactotron from '../config/ReactotronConfig';

// Create the saga middleware with Reactotron monitoring
const sagaMiddleware = createSagaMiddleware({
  sagaMonitor: __DEV__ ? Reactotron.createSagaMonitor?.() : undefined,
});

// Configure the store
const store = configureStore({
  reducer: {
    auth: authReducer,
    chatDB: chatDBReducer,
    socket: socketReducer,
    typing: typingReducer,
    emojiReaction: emojiReactionReducer,
    // Add other reducers here as needed
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      thunk: false, // Disable thunk since we're using saga
      serializableCheck: {
        // Ignore these action types if needed
        ignoredActions: [],
        // Ignore these field paths in all actions
        ignoredActionPaths: [],
        // Ignore these paths in the state
        ignoredPaths: [],
      },
    }).concat(sagaMiddleware),
  enhancers: (getDefaultEnhancers) => {
    if (__DEV__ && Reactotron.createEnhancer) {
      return getDefaultEnhancers().concat(Reactotron.createEnhancer());
    }
    return getDefaultEnhancers();
  },
});

// Run the root saga
sagaMiddleware.run(rootSaga);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
