import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { colors, radius, spacing, typography } from '../theme';

interface MessageItemProps {
  message?: any;
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  if (!message) return null;

  if (message.type === 'clear_chat') {
    return (
      <View style={styles.systemMessageContainer}>
        <View style={[
          styles.systemMessageContent,
          message.status === 'sending' && styles.sendingGradient
        ]}>
          <Text style={styles.systemMessageText}>
            Chat cleared by {message.is_mine ? 'you' : message.sender_username}
          </Text>
          <Text style={styles.messageTime}>{formatTime(message.timestamp)}</Text>
        </View>
      </View>
    );
  }

  if (message.type === 'typing') return null;

  const renderStatusIcon = () => {
    if (!message.isMine && !message.is_mine) return null;

    switch (message.status) {
      case 'sending':
        return (
          <View style={styles.statusContainer}>
            <ActivityIndicator size="small" color="#999" />
          </View>
        );
      case 'sent':
        return (
          <View style={styles.statusContainer}>
            <Text style={styles.singleTick}>✓</Text>
          </View>
        );
      case 'delivered':
      case 'read':
        return (
          <View style={styles.statusContainer}>
            <Text style={styles.doubleTick}>✓✓</Text>
          </View>
        );
      default:
        return null;
    }
  };

  const isMine = message.isMine || message.is_mine;

  return (
    <View style={[
      styles.messageContainer,
      isMine ? styles.sentMessage : styles.receivedMessage,
      isLastInGroup && styles.lastInGroup
    ]}>
      <View style={[
        styles.messageContent,
        isMine ? styles.sentContent : styles.receivedContent,
        message.status === 'sending' && styles.sendingGradient
      ]}>
        <Text style={[
          styles.messageText,
          isMine ? styles.sentText : styles.receivedText
        ]}>
          {message.message}
        </Text>
        <View style={styles.messageInfo}>
          <Text style={[
            styles.messageTime,
            isMine ? styles.sentTime : styles.receivedTime
          ]}>
            {formatTime(message.timestamp)}
          </Text>
          {renderStatusIcon()}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    marginVertical: 2,
    marginHorizontal: spacing.md,
  },
  sentMessage: {
    alignItems: 'flex-end',
  },
  receivedMessage: {
    alignItems: 'flex-start',
  },
  lastInGroup: {
    marginBottom: spacing.sm,
  },
  messageContent: {
    maxWidth: '80%',
    borderRadius: radius.md, // 12px to match frontend
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  sentContent: {
    backgroundColor: colors.primaryTint, // matches frontend .sent .message-content
  },
  receivedContent: {
    backgroundColor: colors.tintLight, // matches frontend .received .message-content
  },
  sendingGradient: {
    opacity: 0.7,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  sentText: {
    color: colors.primary, // matches frontend .sent .message-content p
  },
  receivedText: {
    color: colors.shadeTwo, // matches frontend .received .message-content p
  },
  messageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  messageTime: {
    ...typography.caption,
    marginRight: spacing.xs,
  },
  sentTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  receivedTime: {
    color: colors.textSecondary,
  },
  statusContainer: {
    marginLeft: spacing.xs,
  },
  singleTick: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  doubleTick: {
    fontSize: 12,
    color: colors.success,
  },
  systemMessageContainer: {
    alignItems: 'center',
    marginVertical: spacing.sm,
  },
  systemMessageContent: {
    backgroundColor: colors.gray100,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    alignItems: 'center',
  },
  systemMessageText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default MessageItem;
