import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Text,
  Alert,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { sendMessageRequest, selectConnected, selectError } from '../redux/slices/socketSlice';
import { selectEmojiReactionsEnabled } from '../redux/slices/emojiReactionSlice';
import EmojiBar from './EmojiBar';

interface MessageInputProps {
  receiverUsername: string;
  onFocus?: () => void;
  onBlur?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({
  receiverUsername,
  onFocus,
  onBlur
}) => {
  const dispatch = useAppDispatch();
  const connected = useAppSelector(selectConnected);
  const error = useAppSelector(selectError);
  const emojiReactionsEnabled = useAppSelector(selectEmojiReactionsEnabled);

  const [message, setMessage] = useState<string>('');
  const [sendStatus, setSendStatus] = useState<{ success: boolean, message: string } | null>(null);
  const inputRef = useRef<TextInput>(null);

  // Watch for errors from Redux
  useEffect(() => {
    if (error && error.includes('Failed to send message')) {
      setSendStatus({
        success: false,
        message: error
      });
    }
  }, [error]);

  // Clear send status after 3 seconds
  useEffect(() => {
    if (sendStatus) {
      const timer = setTimeout(() => {
        setSendStatus(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [sendStatus]);

  const handleSubmit = () => {
    if (!message.trim() || !receiverUsername || !connected) {
      if (!connected) {
        setSendStatus({
          success: false,
          message: 'Not connected to server'
        });
      }
      return;
    }

    // Send as a text message type
    dispatch(sendMessageRequest({
      receiverUsername,
      messageText: message.trim(),
      messageType: 'text'
    }));
    setMessage('');

    // Focus back on the input after sending
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Typing indicator state
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Send typing indicator
  const sendTypingIndicator = (typing: boolean) => {
    if (connected && receiverUsername) {
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: typing ? 'typing' : 'stopped_typing',
        messageType: 'typing'
      }));
    }
  };

  // Handle text input change and typing indicator
  const handleTextChange = (text: string) => {
    setMessage(text);

    // Handle typing indicator
    if (text.trim() && !isTyping) {
      // User started typing
      setIsTyping(true);
      sendTypingIndicator(true);
    } else if (!text.trim() && isTyping) {
      // User stopped typing
      setIsTyping(false);
      sendTypingIndicator(false);
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a timeout to stop the typing indicator after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        sendTypingIndicator(false);
      }
    }, 3000);
  };

  // Clean up typing indicator on unmount
  useEffect(() => {
    return () => {
      // Clear typing timeout and send stopped typing on unmount
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      if (isTyping && connected && receiverUsername) {
        sendTypingIndicator(false);
      }
    };
  }, [isTyping, connected, receiverUsername]);

  // Handle emoji click
  const handleEmojiClick = (emoji: string) => {
    // Currently empty in frontend, could be used for inserting emoji into text
  };

  // Handle emoji long press
  const handleEmojiLongPress = (emoji: string) => {
    if (connected && receiverUsername) {
      // Get the mood name from the emoji
      let mood = '';
      switch (emoji) {
        case '😊': mood = 'happy'; break;
        case '😂': mood = 'laughing'; break;
        case '😡': mood = 'angry'; break;
        case '😢': mood = 'sad'; break;
        case '❤️': mood = 'love'; break;
        case '👍': mood = 'thumbsUp'; break;
        default: mood = 'feeling';
      }

      // Send emoji reaction message with emoji and mood
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: `${emoji}:${mood}`,
        messageType: 'emoji_reaction'
      }));
    }
  };

  // Handle emoji release
  const handleEmojiRelease = () => {
    if (connected && receiverUsername) {
      // Send message to stop showing emoji reaction
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'stopped_reaction',
        messageType: 'emoji_reaction'
      }));
    }
  };

  return (
    <View style={styles.container}>
      {sendStatus && (
        <View style={[
          styles.sendStatus,
          sendStatus.success ? styles.successStatus : styles.errorStatus
        ]}>
          <Text style={styles.statusText}>{sendStatus.message}</Text>
        </View>
      )}

      {/* Emoji Bar */}
      {emojiReactionsEnabled && (
        <EmojiBar
          onEmojiClick={handleEmojiClick}
          onEmojiLongPress={handleEmojiLongPress}
          onEmojiRelease={handleEmojiRelease}
        />
      )}

      <View style={styles.inputContainer}>
        <TextInput
          ref={inputRef}
          style={styles.textInput}
          value={message}
          onChangeText={handleTextChange}
          placeholder={connected ? 'Type a message...' : 'Connect to send messages'}
          editable={connected && !!receiverUsername}
          multiline
          onFocus={onFocus}
          onBlur={onBlur}
          returnKeyType="send"
          onSubmitEditing={handleSubmit}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            (!connected || !message.trim() || !receiverUsername) && styles.disabledButton
          ]}
          onPress={handleSubmit}
          disabled={!connected || !message.trim() || !receiverUsername}
        >
          <Text style={styles.sendButtonText}>Send</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  sendStatus: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  successStatus: {
    backgroundColor: '#4CAF50',
  },
  errorStatus: {
    backgroundColor: '#FF5722',
  },
  statusText: {
    color: '#ffffff',
    fontSize: 14,
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 16,
    backgroundColor: '#f8f8f8',
  },
  sendButton: {
    backgroundColor: '#2196F3',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#cccccc',
  },
  sendButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default MessageInput;
