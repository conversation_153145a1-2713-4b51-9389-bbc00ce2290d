import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useAppSelector, useAppDispatch } from '../hooks/redux';
import { selectConnected, sendMessageRequest, connectRequest } from '../redux/slices/socketSlice';
import { selectAuthUser, selectIsAuthenticated, selectAuthToken } from '../redux/slices/authSlice';
import { selectIsUserTyping } from '../redux/slices/typingSlice';
import { selectUserEmojiReaction } from '../redux/slices/emojiReactionSlice';
import { clearCurrentReceiver, selectCurrentReceiverUsername } from '../redux/slices/chatDBSlice';
import UserInfo from './UserInfo';
import ClearChatModal from './ClearChatModal';
import DeleteUserModal from './DeleteUserModal';
import MessageInput from './MessageInput';
import MessageItem from './MessageItem';

interface Message {
  id: string;
  room_id: string;
  sender_username: string;
  receiver_username: string;
  message: string;
  type?: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'emoji_reaction';
  timestamp: number;
  status: string;
  is_mine: boolean;
}

type MessageRecord = Record<string, any> | Message;

interface ChatWindowProps {
  messages: MessageRecord[];
  receiverUsername: string | null;
  onClearChat?: () => void;
  onBackToRooms?: () => void;
  onShowProfile?: () => void;
}

const ChatWindow: React.FC<ChatWindowProps> = ({ 
  messages = [], 
  receiverUsername = null, 
  onClearChat, 
  onBackToRooms, 
  onShowProfile 
}) => {
  const dispatch = useAppDispatch();
  const connected = useAppSelector(selectConnected);
  const currentUser = useAppSelector(selectAuthUser);
  const selectedReceiverUsername = useAppSelector(selectCurrentReceiverUsername);
  const flatListRef = useRef<FlatList>(null);

  const [showClearModal, setShowClearModal] = useState<boolean>(false);
  const [clearingChat, setClearingChat] = useState<boolean>(false);
  const [showDeleteUserModal, setShowDeleteUserModal] = useState<boolean>(false);
  const [deletingUser, setDeletingUser] = useState<boolean>(false);
  const [chatCleared, setChatCleared] = useState<boolean>(false);

  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const authToken = useAppSelector(selectAuthToken);

  // Get typing status from Redux
  const isTyping = receiverUsername ? useAppSelector((state) =>
    selectIsUserTyping(receiverUsername)(state)
  ) : false;

  // Get emoji reaction from Redux (for received reactions)
  const emojiReaction = receiverUsername ? useAppSelector((state) =>
    selectUserEmojiReaction(receiverUsername)(state)
  ) : null;

  // Get our own emoji reaction (for sent reactions)
  const myEmojiReaction = currentUser ? useAppSelector((state) =>
    selectUserEmojiReaction(currentUser)(state)
  ) : null;

  // Format timestamp if available
  const formatTime = (timestamp: number): string => {
    if (!timestamp) return '';

    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      return '';
    }
  };

  // Group messages by date
  const getMessageDate = (timestamp: number): string => {
    if (!timestamp) return '';

    try {
      const date = new Date(timestamp);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      if (date.toDateString() === today.toDateString()) {
        return 'Today';
      } else if (date.toDateString() === yesterday.toDateString()) {
        return 'Yesterday';
      } else {
        return date.toLocaleDateString(undefined, {
          weekday: 'long',
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      return '';
    }
  };

  // Handle opening the clear chat modal
  const handleOpenClearModal = () => {
    setShowClearModal(true);
  };

  // Handle closing the clear chat modal
  const handleCloseClearModal = () => {
    setShowClearModal(false);
  };

  // Handle clearing the chat
  const handleClearChat = async () => {
    if (!currentUser || !receiverUsername) return;

    try {
      setClearingChat(true);

      // First, send a clear_chat type message to notify the other user to clear their chat
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'Chat cleared',
        messageType: 'clear_chat'
      }));

      // Set chat cleared state to true
      setChatCleared(true);

      // Call the parent component's onClearChat callback if provided
      if (onClearChat) {
        onClearChat();
      }

      setShowClearModal(false);
    } catch (error) {
      console.error('Failed to clear chat:', error);
    } finally {
      setClearingChat(false);
    }
  };

  // Handle opening the delete user modal
  const handleOpenDeleteUserModal = () => {
    setShowDeleteUserModal(true);
  };

  // Handle closing the delete user modal
  const handleCloseDeleteUserModal = () => {
    setShowDeleteUserModal(false);
  };

  // Handle deleting the user
  const handleDeleteUser = async () => {
    if (!currentUser || !receiverUsername) return;

    try {
      setDeletingUser(true);

      // Send a delete_user type message to notify the other user to delete the room
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'User deleted',
        messageType: 'delete_user'
      }));

      // Clear the current receiver to close the chat window
      dispatch(clearCurrentReceiver());

      setShowDeleteUserModal(false);
    } catch (error) {
      console.error('Failed to delete user:', error);
    } finally {
      setDeletingUser(false);
    }
  };

  const chatStatus = connected
    ? (isTyping ? 'Typing' : 'Online')
    : 'Offline';

  // Auto-reconnect mechanism
  useEffect(() => {
    let reconnectTimer: NodeJS.Timeout | null = null;

    // If authenticated but not connected, try to reconnect
    if (isAuthenticated && authToken && !connected) {
      console.log('Socket disconnected, attempting to reconnect...');
      reconnectTimer = setTimeout(() => {
        console.log('Reconnecting to socket...');
        dispatch(connectRequest({ authToken }));
      }, 3000); // Try to reconnect after 3 seconds
    }

    // Clean up timer on unmount
    return () => {
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
      }
    };
  }, [isAuthenticated, authToken, connected, dispatch]);

  const handleUserClick = () => {
    if (onShowProfile) {
      onShowProfile();
    }
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
      
      // Reset chatCleared state if new messages arrive
      setChatCleared(false);
    }
  }, [messages]);

  const renderMessage = ({ item, index }: { item: MessageRecord; index: number }) => {
    if (!item) return null;

    const messageTimestamp = item.timestamp || Date.now();
    const nextMessage = messages[index + 1];
    const isLastInGroup =
      !nextMessage ||
      nextMessage.is_mine !== item.is_mine ||
      getMessageDate(messageTimestamp) !== getMessageDate(nextMessage?.timestamp || 0);

    return (
      <MessageItem
        message={item}
        formatTime={formatTime}
        isLastInGroup={isLastInGroup}
      />
    );
  };

  const renderDateSeparator = (date: string) => (
    <View style={styles.dateSeparator}>
      <Text style={styles.dateText}>{date}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        {receiverUsername && (
          <>
            {onBackToRooms && (
              <TouchableOpacity style={styles.backButton} onPress={onBackToRooms}>
                <Text style={styles.backButtonText}>←</Text>
              </TouchableOpacity>
            )}
            <UserInfo
              userId={receiverUsername}
              status={chatStatus}
              disableEmoji={true}
              onClick={handleUserClick}
              style={styles.userInfo}
            />
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleOpenClearModal}
              >
                <Text style={styles.actionButtonText}>🔄</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleOpenDeleteUserModal}
              >
                <Text style={styles.actionButtonText}>🗑️</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>

      {/* Connection status banner */}
      {isAuthenticated && !connected && (
        <View style={styles.connectionBanner}>
          <Text style={styles.connectionText}>Reconnecting...</Text>
        </View>
      )}

      {/* Messages */}
      <View style={styles.messagesContainer}>
        {messages.length > 0 ? (
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item, index) => item.id || `msg-${index}`}
            style={styles.messagesList}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.noMessages}>
            <Text style={styles.noMessagesText}>
              {!receiverUsername
                ? 'Select a contact to view messages'
                : !connected
                  ? 'Connect to a server to receive messages.'
                  : chatCleared
                    ? 'Chat has been cleared'
                    : 'No messages yet. Start the conversation!'}
            </Text>
          </View>
        )}

        {/* Emoji reactions */}
        {emojiReaction && (
          <View style={styles.emojiReactionContainer}>
            <Text style={styles.emojiReaction}>{emojiReaction.emoji}</Text>
          </View>
        )}

        {myEmojiReaction && (
          <View style={[styles.emojiReactionContainer, styles.myEmojiReaction]}>
            <Text style={styles.emojiReaction}>{myEmojiReaction.emoji}</Text>
          </View>
        )}
      </View>

      {/* Message Input */}
      <MessageInput receiverUsername={selectedReceiverUsername || ''} />

      {/* Modals */}
      <ClearChatModal
        isVisible={showClearModal}
        onClose={handleCloseClearModal}
        onConfirm={handleClearChat}
        loading={clearingChat}
      />

      <DeleteUserModal
        isVisible={showDeleteUserModal}
        onClose={handleCloseDeleteUserModal}
        onConfirm={handleDeleteUser}
        loading={deletingUser}
        username={receiverUsername || undefined}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  backButton: {
    marginRight: 12,
    padding: 8,
  },
  backButtonText: {
    fontSize: 24,
    color: '#2196F3',
  },
  userInfo: {
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 12,
    padding: 8,
  },
  actionButtonText: {
    fontSize: 20,
  },
  connectionBanner: {
    backgroundColor: '#FF9800',
    paddingVertical: 8,
    alignItems: 'center',
  },
  connectionText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  messagesContainer: {
    flex: 1,
  },
  messagesList: {
    flex: 1,
    paddingVertical: 8,
  },
  noMessages: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  noMessagesText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  dateSeparator: {
    alignItems: 'center',
    marginVertical: 16,
  },
  dateText: {
    backgroundColor: '#e0e0e0',
    color: '#666',
    fontSize: 12,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  emojiReactionContainer: {
    alignItems: 'flex-start',
    marginHorizontal: 16,
    marginVertical: 4,
  },
  myEmojiReaction: {
    alignItems: 'flex-end',
  },
  emojiReaction: {
    fontSize: 24,
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
});

export default ChatWindow;
